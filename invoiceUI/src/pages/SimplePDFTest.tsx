import { useState } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

export default function SimplePDFTest() {
  const [numPages, setNumPages] = useState<number | null>(null)
  const [pageNumber, setPageNumber] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const pdfUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    console.log('PDF loaded successfully! Pages:', numPages)
    setNumPages(numPages)
    setLoading(false)
    setError('')
  }

  function onDocumentLoadError(error: any) {
    console.error('PDF load error:', error)
    console.error('PDF.js version:', pdfjs.version)
    console.error('Worker src:', pdfjs.GlobalWorkerOptions.workerSrc)
    setError(`Failed to load PDF: ${error.message}`)
    setLoading(false)
  }

  function onDocumentLoadStart() {
    console.log('PDF load started for:', pdfUrl)
    console.log('PDF.js version:', pdfjs.version)
    console.log('Worker src:', pdfjs.GlobalWorkerOptions.workerSrc)
    setLoading(true)
    setError('')
  }

  return (
    <div className="h-screen bg-gray-50 overflow-auto flex flex-col">
      <nav className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-xl font-semibold text-gray-900">Simple PDF Test</h1>
        <p className="text-sm text-gray-600">Testing basic PDF.js functionality</p>
      </nav>
      
      <div className="flex-1 p-6">
        <div className="bg-white shadow-sm rounded-lg p-6">
          <div className="mb-4">
            <h2 className="text-lg font-medium mb-2">PDF Information</h2>
            <p className="text-sm text-gray-600">URL: {pdfUrl}</p>
            <p className="text-sm text-gray-600">PDF.js version: {pdfjs.version}</p>
            <p className="text-sm text-gray-600">Worker: {pdfjs.GlobalWorkerOptions.workerSrc}</p>
            {numPages && <p className="text-sm text-gray-600">Total pages: {numPages}</p>}
          </div>

          {loading && (
            <div className="flex items-center justify-center p-8">
              <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mr-2" />
              <span>Loading PDF...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          <div className="flex flex-col items-center">
            <Document
              file={pdfUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
              onLoadStart={onDocumentLoadStart}
              loading={<div>Loading document...</div>}
              error={<div>Error loading document</div>}
              options={{
                cMapUrl: '/cmaps/',
                cMapPacked: true,
                standardFontDataUrl: '/standard_fonts/',
              }}
            >
              {numPages && (
                <div className="space-y-4">
                  <div className="flex items-center justify-center space-x-4 mb-4">
                    <button
                      onClick={() => setPageNumber(Math.max(1, pageNumber - 1))}
                      disabled={pageNumber <= 1}
                      className="px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300"
                    >
                      Previous
                    </button>
                    <span className="text-sm">
                      Page {pageNumber} of {numPages}
                    </span>
                    <button
                      onClick={() => setPageNumber(Math.min(numPages, pageNumber + 1))}
                      disabled={pageNumber >= numPages}
                      className="px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300"
                    >
                      Next
                    </button>
                  </div>
                  
                  <Page
                    pageNumber={pageNumber}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    className="shadow-lg border border-gray-300"
                    width={600}
                  />
                </div>
              )}
            </Document>
          </div>
        </div>
      </div>
    </div>
  )
}
