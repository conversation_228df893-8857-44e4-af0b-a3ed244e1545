import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "react-router-dom"
import {
  Bell,
  Search,
  User,
  ChevronRight,
  Download,
  Edit,
  Receipt,
  Database,
  Check,
  X,
  Eye,
  CheckCircle,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Link } from "react-router-dom"
import { getInvoiceDetails, type InvoiceDetailsResponse } from "@/utils/api"
import { UniversalDocumentViewer } from "@/components/UniversalDocumentViewer"

// Mock data removed - now using real API data

const StatusIcon = ({ status }: { status: string }) => {
  switch (status) {
    case "Published":
      return (
        <svg width="14" height="14" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="8" cy="8" r="8" fill="#10B981" />
          <path
            d="M11.3333 5.5L6.75 10.0833L4.66667 8"
            stroke="white"
            strokeWidth="1.3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )
    case "Pending":
      return (
        <svg width="14" height="14" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="8" cy="8" r="8" fill="#F59E0B" />
          <path
            d="M8 4.66667V8L10.6667 9.33333"
            stroke="white"
            strokeWidth="1.3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )
    case "Rejected":
      return (
        <svg width="14" height="14" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="8" cy="8" r="8" fill="#EF4444" />
          <path d="M10 6L6 10" stroke="white" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M6 6L10 10" stroke="white" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      )
    default:
      return null
  }
}

const getStatusBadge = (status: string) => {
  const baseClasses = "inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium gap-1.5"

  switch (status) {
    case "Published":
      return (
        <div className={`${baseClasses} bg-green-50 text-green-700 border border-green-200`}>
          <StatusIcon status={status} />
          Published
        </div>
      )
    case "Pending":
      return (
        <div className={`${baseClasses} bg-orange-50 text-orange-700 border border-orange-200`}>
          <StatusIcon status={status} />
          Pending
        </div>
      )
    case "Rejected":
      return (
        <div className={`${baseClasses} bg-red-50 text-red-700 border border-red-200`}>
          <StatusIcon status={status} />
          Rejected
        </div>
      )
    default:
      return <div className={`${baseClasses} bg-gray-50 text-gray-700 border border-gray-200`}>{status}</div>
  }
}

export default function InvoiceDetail() {
  const { id } = useParams()

  // API data state
  const [invoiceData, setInvoiceData] = useState<InvoiceDetailsResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  // Document viewer state (handled by UniversalDocumentViewer)

  // UI state
  const [invoiceStatus, setInvoiceStatus] = useState("")
  const [activityLog, setActivityLog] = useState<
    Array<{
      id: string
      timestamp: Date
      action: string
      status: "success" | "error" | "info"
      details: string
      user: string
    }>
  >([])

  // Fetch invoice details from API
  useEffect(() => {
    const fetchInvoiceDetails = async () => {
      if (!id) {
        setError("Invoice ID is required")
        return
      }

      setIsLoading(true)
      setError("")

      try {
        const response = await getInvoiceDetails(id)
        setInvoiceData(response)
        setInvoiceStatus(response.formatted.status)

        // Debug: Log PDF URL
        console.log("PDF URL:", response.invoice.uploaded_file_path)

        // Start page will be handled by UniversalDocumentViewer

        // Initialize activity log with creation entry
        setActivityLog([
          {
            id: "1",
            timestamp: new Date(response.formatted.added_date),
            action: "Invoice Created",
            status: "info",
            details: `Invoice uploaded and processed via ${response.formatted.source.toLowerCase()}`,
            user: response.formatted.uploaded_by || "System",
          },
        ])
      } catch (error) {
        console.error("Error fetching invoice details:", error)
        setError(error instanceof Error ? error.message : "Failed to load invoice details")
      } finally {
        setIsLoading(false)
      }
    }

    fetchInvoiceDetails()
  }, [id])

  // PDF handlers removed - now handled by UniversalDocumentViewer

  // Helper functions
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      })
    } catch {
      return dateString
    }
  }





  const handleApprove = () => {
    if (!invoiceData) return

    // Add to activity log
    const newLogEntry = {
      id: Date.now().toString(),
      timestamp: new Date(),
      action: invoiceStatus === "Rejected" ? "Invoice Re-approved & Published" : "Invoice Approved & Published",
      status: "success" as const,
      details: invoiceStatus === "Rejected"
        ? `Invoice was re-approved after rejection and successfully posted to QuickBooks. Vendor: ${invoiceData.formatted.vendor} already exists in system.`
        : `Invoice approved and successfully posted to QuickBooks. Vendor: ${invoiceData.formatted.vendor} already exists in system.`,
      user: "Sarah Johnson",
    }
    setActivityLog((prev) => [...prev, newLogEntry])

    // Update status immediately
    setInvoiceStatus("Published")
  }

  const handleReject = () => {
    const newLogEntry = {
      id: Date.now().toString(),
      timestamp: new Date(),
      action: "Invoice Rejected",
      status: "error" as const,
      details: "Invoice rejected due to validation issues or manual review decision.",
      user: "Sarah Johnson",
    }
    setActivityLog((prev) => [...prev, newLogEntry])
    setInvoiceStatus("Rejected")
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
          <span className="text-gray-600">Loading invoice details...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-2">{error}</div>
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
            className="text-red-600 hover:text-red-700"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  // No data state
  if (!invoiceData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-600">Invoice not found</div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-gray-50 overflow-hidden flex flex-col">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-pink-400 rounded-lg flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-sm"></div>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <Link to="/dashboard">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900 font-medium px-4 py-2 h-auto">
                  Dashboard
                </Button>
              </Link>
              <Link to="/invoices">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900 font-medium px-4 py-2 h-auto">
                  Invoices
                </Button>
              </Link>
              <Link to="/admin">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900 font-medium px-4 py-2 h-auto">
                  Admin Settings
                </Button>
              </Link>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon">
              <Search className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
            </Button>
            <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="flex-1 p-6 overflow-hidden">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-xs text-gray-500 mb-6">
          <Link to="/invoices" className="hover:text-gray-700">
            Invoices
          </Link>
          <ChevronRight className="h-3 w-3" />
          <span className="text-gray-700 font-medium">{invoiceData.formatted.invoice_number}</span>
        </nav>

        {/* Invoice Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{invoiceData.formatted.invoice_number}</h1>
              <div className="flex items-center space-x-4 text-sm">
                <span className="text-gray-600">
                  From: <span className="font-medium text-gray-900">{invoiceData.formatted.vendor}</span>
                </span>
                <span className="text-gray-600">•</span>
                <span className="text-gray-600">
                  Amount: <span className="font-medium text-gray-900">
                    {formatCurrency(invoiceData.formatted.amount, invoiceData.formatted.currency)}
                  </span>
                </span>
                <span className="text-gray-600">•</span>
                {getStatusBadge(invoiceStatus)}
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {(invoiceStatus === "Pending" || invoiceStatus === "Rejected") && (
                <>
                  <Button onClick={handleApprove} className="bg-green-600 hover:bg-green-700 text-white" size="sm">
                    <Check className="h-4 w-4 mr-2" />
                    {invoiceStatus === "Rejected" ? "Re-approve & Publish" : "Approve & Publish"}
                  </Button>
                  <Button
                    onClick={handleReject}
                    variant="outline"
                    className="border-red-200 text-red-600 hover:bg-red-50 bg-transparent"
                    size="sm"
                  >
                    <X className="h-4 w-4 mr-2" />
                    {invoiceStatus === "Rejected" ? "Keep Rejected" : "Reject"}
                  </Button>
                </>
              )}

              {invoiceStatus === "Published" && (
                <div className="flex items-center space-x-3">
                  <Button disabled className="bg-green-600 text-white cursor-default" size="sm">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Published
                  </Button>
                  <Button
                    onClick={handleReject}
                    variant="outline"
                    className="border-red-200 text-red-600 hover:bg-red-50 bg-transparent"
                    size="sm"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Reject
                  </Button>
                </div>
              )}

              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View Activity Log
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh]">
                  <DialogHeader>
                    <DialogTitle>Activity Log - {invoiceData.formatted.invoice_number}</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {activityLog.map((log) => (
                      <div key={log.id} className="border-l-4 border-gray-200 pl-4 py-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  log.status === "success"
                                    ? "bg-green-500"
                                    : log.status === "error"
                                      ? "bg-red-500"
                                      : "bg-blue-500"
                                }`}
                              ></div>
                              <span className="font-medium text-gray-900">{log.action}</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{log.details}</p>
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span>
                                {log.timestamp.toLocaleDateString()} at {log.timestamp.toLocaleTimeString()}
                              </span>
                              <span>•</span>
                              <span>by {log.user}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>



        {/* Main Content Grid */}
        <div className="grid grid-cols-12 gap-6 h-full overflow-hidden">
          {/* Left Column - Invoice Image */}
          <div className="col-span-7 flex flex-col h-full">
            <Card className="bg-white shadow-sm flex flex-col h-full">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                    <Receipt className="h-5 w-5 mr-2" />
                    Document Viewer
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (invoiceData.invoice.uploaded_file_path) {
                        window.open(invoiceData.invoice.uploaded_file_path, '_blank')
                      }
                    }}
                    disabled={!invoiceData.invoice.uploaded_file_path}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="flex-1 p-0">
                {/* Universal Document Viewer */}
                <UniversalDocumentViewer
                  src={invoiceData.invoice.uploaded_file_path}
                  contentType={invoiceData.job_info?.content_type}
                  filename={invoiceData.job_info?.original_filename}
                  fileSize={invoiceData.job_info?.file_size}
                  startPage={invoiceData.invoice.start_page}
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Structured Data */}
          <div className="col-span-5">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                    <Database className="h-5 w-5 mr-2" />
                    Structured Data
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Raw Text
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh]">
                        <DialogHeader>
                          <DialogTitle>Extracted Text</DialogTitle>
                        </DialogHeader>
                        <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                          <pre className="text-xs text-gray-700 whitespace-pre-wrap font-mono leading-relaxed">
                            {invoiceData.invoice.extracted_text || "No extracted text available"}
                          </pre>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Invoice Information</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Invoice Number:</span>
                      <p className="font-medium">{invoiceData.formatted.invoice_number}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Vendor:</span>
                      <p className="font-medium">{invoiceData.formatted.vendor}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Invoice Date:</span>
                      <p className="font-medium">{formatDate(invoiceData.formatted.invoice_date)}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Due Date:</span>
                      <p className="font-medium">
                        {invoiceData.formatted.due_date ? formatDate(invoiceData.formatted.due_date) : "N/A"}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-600">Category:</span>
                      <p className="font-medium">{invoiceData.formatted.category}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Source:</span>
                      <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                        {invoiceData.formatted.source}
                      </Badge>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Line Items Summary */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">
                    Line Items ({invoiceData.formatted.line_items?.length || 0})
                  </h4>
                  <div className="space-y-3">
                    {invoiceData.formatted.line_items && invoiceData.formatted.line_items.length > 0 ? (
                      invoiceData.formatted.line_items.map((item, index) => (
                        <div key={index} className="flex justify-between items-center text-sm p-3 bg-gray-50 rounded">
                          <div>
                            <p className="font-medium text-gray-900">{item.description}</p>
                            <p className="text-gray-600">
                              Qty: {item.quantity} × {formatCurrency(item.unit_price, invoiceData.formatted.currency)}
                            </p>
                          </div>
                          <span className="font-medium">
                            {formatCurrency(item.total_price, invoiceData.formatted.currency)}
                          </span>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500 text-sm">No line items available</p>
                    )}
                  </div>
                </div>

                <Separator />

                {/* Accounting Head */}
                {invoiceData.invoice.accounting_head_match && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Accounting Head</h4>
                    <div className="text-sm">
                      <div className="bg-gray-50 p-3 rounded">
                        <span className="font-medium">{invoiceData.invoice.accounting_head_match.suggested_accounting_head}</span>
                      </div>
                    </div>
                  </div>
                )}

                <Separator />

                {/* Financial Summary */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Financial Summary</h4>
                  <div className="space-y-2 text-sm">
                    {invoiceData.formatted.tax_amount && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tax:</span>
                        <span className="font-medium">
                          {formatCurrency(invoiceData.formatted.tax_amount, invoiceData.formatted.currency)}
                        </span>
                      </div>
                    )}
                    <Separator />
                    <div className="flex justify-between text-base font-bold">
                      <span>Total Amount:</span>
                      <span>{formatCurrency(invoiceData.formatted.amount, invoiceData.formatted.currency)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
