import { PDFViewer } from "@/components/PDFViewer"

export default function TestPDFViewer() {
  // Test with a local PDF file to avoid CORS issues
  const testPdfUrl = '/test.pdf'
  
  return (
    <div className="h-screen bg-gray-50 overflow-hidden flex flex-col">
      <nav className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-xl font-semibold text-gray-900">PDF Viewer Test</h1>
      </nav>
      <div className="flex-1 p-6 overflow-hidden">
        <div className="h-full">
          <div className="bg-white shadow-sm rounded-lg h-full">
            <div className="p-4 border-b">
              <h2 className="text-lg font-medium">Testing PDF Viewer with Sample PDF</h2>
              <p className="text-sm text-gray-600">URL: {testPdfUrl}</p>
            </div>
            <div className="flex-1 h-full">
              <PDFViewer
                src={testPdfUrl}
                filename="test-document.pdf"
                startPage={1}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
