import { useState, useEffect, useMemo } from "react"
import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'
import { But<PERSON> } from "@/components/ui/button"
import { Download, ZoomIn, ZoomOut, FileX } from "lucide-react"
import { getAuthHeaders } from "@/utils/api"

// Configure PDF.js worker - use local worker file that matches react-pdf version
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

// Debug: Log worker configuration
console.log('PDF.js version:', pdfjs.version)
console.log('Worker src:', pdfjs.GlobalWorkerOptions.workerSrc)

interface PDFViewerProps {
  src: string
  filename?: string
  fileSize?: number
  startPage?: number
  onError?: (error: string) => void
  onLoad?: () => void
}

export const PDFViewer = ({ src, filename, fileSize, startPage = 1, onError, onLoad }: PDFViewerProps) => {
  const [numPages, setNumPages] = useState<number | null>(null)
  const [currentPage, setCurrentPage] = useState(startPage)
  const [pdfLoading, setPdfLoading] = useState(false)
  const [pdfError, setPdfError] = useState("")
  const [useIframeViewer, setUseIframeViewer] = useState(false)
  const [scale, setScale] = useState(1.0)

  // Debug: Log PDF source and state
  console.log('PDFViewer - src:', src)
  console.log('PDFViewer - filename:', filename)
  console.log('PDFViewer - startPage:', startPage)
  console.log('PDFViewer - pdfLoading:', pdfLoading)
  console.log('PDFViewer - pdfError:', pdfError)
  console.log('PDFViewer - numPages:', numPages)

  // Test with a known working PDF URL if src is problematic
  const testPdfUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
  console.log('Test PDF URL available:', testPdfUrl)

  // Use test PDF only if no src is provided or there's an error
  const shouldUseTestPdf = !src || pdfError
  const actualSrc = shouldUseTestPdf ? 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' : src
  console.log('Using PDF source:', actualSrc, shouldUseTestPdf ? '(test PDF - no source or error)' : '(original)')

  // Add validation for PDF source
  if (!actualSrc) {
    console.error('No PDF source provided')
    return (
      <div className="flex items-center justify-center h-96 text-gray-500">
        <div className="text-center">
          <FileX className="h-12 w-12 mx-auto mb-2" />
          <p>No PDF source available</p>
        </div>
      </div>
    )
  }

  // Progressive loading state
  const [loadedPages, setLoadedPages] = useState<Set<number>>(new Set())
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  // Memoize PDF.js options to prevent unnecessary reloads
  const pdfOptions = useMemo(() => {
    const options: any = {
      cMapUrl: '/cmaps/',
      cMapPacked: true,
      standardFontDataUrl: '/standard_fonts/',
    }

    // Add authentication headers if loading from API
    if (actualSrc && actualSrc.includes('localhost:8000')) {
      const authHeaders = getAuthHeaders()
      options.httpHeaders = authHeaders
      options.withCredentials = false // Set to false for localhost CORS
    }

    return options
  }, [actualSrc])

  // Memoize the file prop to prevent unnecessary reloads
  const fileConfig = useMemo(() => {
    if (actualSrc.includes('localhost:8000')) {
      return { url: actualSrc, ...pdfOptions }
    }
    return actualSrc
  }, [actualSrc, pdfOptions])

  // Progressive loading functions
  const getInitialPages = (totalPages: number, startPage: number): Set<number> => {
    const pages = new Set<number>()

    // Load more pages initially for better continuous scroll experience
    const initialLoadCount = Math.min(totalPages, 8) // Load first 8 pages or all if fewer

    for (let i = 1; i <= initialLoadCount; i++) {
      pages.add(i)
    }

    // Also ensure start page area is loaded if beyond initial range
    if (startPage > initialLoadCount) {
      for (let i = Math.max(1, startPage - 2); i <= Math.min(totalPages, startPage + 2); i++) {
        pages.add(i)
      }
    }

    return pages
  }

  const loadMorePages = (currentLoadedPages: Set<number>, totalPages: number, batchSize: number = 5): Set<number> => {
    const newPages = new Set(currentLoadedPages)
    const maxLoaded = Math.max(...Array.from(currentLoadedPages))

    // Load larger batches for smoother experience
    for (let i = maxLoaded + 1; i <= Math.min(totalPages, maxLoaded + batchSize); i++) {
      newPages.add(i)
    }

    return newPages
  }

  // PDF viewer handlers
  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    console.log("PDF loaded successfully! Pages:", numPages)
    setNumPages(numPages)
    setPdfLoading(false)
    setPdfError("")

    // Simplified loading strategy
    let initialPages: Set<number>
    if (numPages <= 10) {
      // For small documents, load all pages immediately
      initialPages = new Set<number>()
      for (let i = 1; i <= numPages; i++) {
        initialPages.add(i)
      }
      console.log("Loading all pages immediately for small document")
    } else {
      // For larger documents, use progressive loading
      initialPages = getInitialPages(numPages, startPage)
      console.log("Using progressive loading for large document")
    }

    console.log("Initial pages to load:", Array.from(initialPages))
    setLoadedPages(initialPages)

    // Debug: Log the state after setting
    setTimeout(() => {
      console.log("LoadedPages state after setting:", Array.from(initialPages))
    }, 100)

    onLoad?.()

    // Scroll to start page after a short delay to ensure pages are rendered
    if (startPage > 1) {
      setTimeout(() => {
        scrollToPage(startPage)
      }, 800) // Slightly longer delay for progressive loading
    }
  }

  // Function to scroll to a specific page
  const scrollToPage = (pageNum: number) => {
    const pageElement = document.getElementById(`pdf-page-${pageNum}`)
    if (pageElement) {
      pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
      setCurrentPage(pageNum)
    }
  }

  const onDocumentLoadError = (error: Error) => {
    console.error("PDF load error:", error)
    console.error("PDF.js version:", pdfjs.version)
    console.error("Worker src:", pdfjs.GlobalWorkerOptions.workerSrc)
    console.error("PDF src:", actualSrc)
    console.error("Error details:", error.name, error.message, error.stack)

    // Set error message - fallback to test PDF will happen on next render
    const errorMsg = `Failed to load PDF document: ${error.message}`
    setPdfError(errorMsg)

    setPdfLoading(false)
    onError?.(error.message)
  }

  const onDocumentLoadStart = () => {
    console.log("PDF load started for:", actualSrc)
    console.log("PDF.js version:", pdfjs.version)
    console.log("Worker src:", pdfjs.GlobalWorkerOptions.workerSrc)
    console.log("PDF load started - checking URL accessibility...")

    // Test if URL is accessible
    const fetchOptions: RequestInit = { method: 'HEAD' }
    if (actualSrc.includes('localhost:8000')) {
      const authHeaders = getAuthHeaders()
      fetchOptions.headers = authHeaders
    }

    fetch(actualSrc, fetchOptions)
      .then(response => {
        console.log("PDF URL accessibility check:", response.status, response.statusText)
        console.log("PDF URL headers:", Object.fromEntries(response.headers.entries()))
      })
      .catch(error => {
        console.error("PDF URL accessibility error:", error)
      })

    setPdfLoading(true)
    setPdfError("")
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = async () => {
    if (src.includes('localhost:8000')) {
      // For authenticated API endpoints, fetch with auth headers and create blob
      try {
        const authHeaders = getAuthHeaders()
        const response = await fetch(src, {
          headers: authHeaders
        })

        if (response.ok) {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = filename || 'document.pdf'
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        } else {
          console.error('Download failed:', response.status, response.statusText)
          // Fallback to opening in new tab
          window.open(src, '_blank')
        }
      } catch (error) {
        console.error('Download error:', error)
        // Fallback to opening in new tab
        window.open(src, '_blank')
      }
    } else {
      // For public URLs, use simple window.open
      window.open(src, '_blank')
    }
  }

  // Track which page is currently visible using Intersection Observer
  useEffect(() => {
    if (!numPages || loadedPages.size === 0) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const pageId = entry.target.id
            const pageNum = parseInt(pageId.replace('pdf-page-', ''))
            if (pageNum && pageNum !== currentPage) {
              setCurrentPage(pageNum)
            }
          }
        })
      },
      {
        threshold: 0.5, // Page is considered visible when 50% is in view
        rootMargin: '-10% 0px -10% 0px' // Add some margin to avoid rapid switching
      }
    )

    // Observe loaded page elements
    Array.from(loadedPages).forEach((pageNum) => {
      const pageElement = document.getElementById(`pdf-page-${pageNum}`)
      if (pageElement) {
        observer.observe(pageElement)
      }
    })

    return () => observer.disconnect()
  }, [numPages, currentPage, loadedPages])

  // Load more pages when user scrolls near the end
  useEffect(() => {
    if (!numPages || isLoadingMore) return

    const loadMoreObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isLoadingMore) {
            const maxLoaded = Math.max(...Array.from(loadedPages))
            if (maxLoaded < numPages) {
              setIsLoadingMore(true)
              // Load remaining pages more aggressively
              setTimeout(() => {
                setLoadedPages(prev => loadMorePages(prev, numPages))
                setIsLoadingMore(false)
              }, 50) // Faster loading
            }
          }
        })
      },
      {
        threshold: 0.1,
        rootMargin: '400px 0px 400px 0px' // Load more when user is 400px away from trigger
      }
    )

    const loadMoreTrigger = document.getElementById('load-more-trigger')
    if (loadMoreTrigger) {
      loadMoreObserver.observe(loadMoreTrigger)
    }

    return () => loadMoreObserver.disconnect()
  }, [numPages, loadedPages, isLoadingMore])

  // Auto-load all pages for small documents and background loading for larger ones
  useEffect(() => {
    if (!numPages) return

    if (numPages <= 15 && loadedPages.size < numPages && !isLoadingMore) {
      // For small documents (15 pages or less), load all pages immediately
      const allPages = new Set<number>()
      for (let i = 1; i <= numPages; i++) {
        allPages.add(i)
      }
      setLoadedPages(allPages)
    } else if (numPages > 15) {
      // For larger documents, gradually load all pages in background
      const loadAllPagesGradually = () => {
        const currentMax = Math.max(...Array.from(loadedPages))
        if (currentMax < numPages && !isLoadingMore) {
          setIsLoadingMore(true)
          setTimeout(() => {
            setLoadedPages(prev => loadMorePages(prev, numPages, 3))
            setIsLoadingMore(false)
          }, 1000) // Load every second in background
        }
      }

      const interval = setInterval(loadAllPagesGradually, 1500)
      return () => clearInterval(interval)
    }
  }, [numPages, loadedPages.size, isLoadingMore])

  return (
    <div className="flex flex-col h-full max-h-screen overflow-hidden">
      {/* PDF Controls */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            {numPages ? `${numPages} pages` : 'Loading...'}
            {currentPage > 1 && ` • Current: Page ${currentPage}`}
          </span>
          {filename && (
            <span className="text-sm text-gray-500">
              • {filename} {fileSize && `(${formatFileSize(fileSize)})`}
            </span>
          )}
          <span className="text-xs text-gray-400">
            • Zoom: {Math.round(scale * 100)}%
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setScale(prev => Math.max(0.5, prev - 0.1))}
            disabled={scale <= 0.5}
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setScale(prev => Math.min(2.0, prev + 0.1))}
            disabled={scale >= 2.0}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setScale(1.0)}
          >
            Reset
          </Button>
          {startPage > 1 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => scrollToPage(startPage)}
            >
              Go to Page {startPage}
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </div>

      {/* PDF Viewer - Scrollable document area only */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden p-4 bg-gray-100 min-h-0">
        {useIframeViewer ? (
          // Simple iframe viewer fallback
          <div className="w-full h-full min-h-[600px]">
            <iframe
              src={`${src}#page=${startPage}&view=FitH`}
              className="w-full h-full border-0 rounded shadow-lg"
              title="Invoice PDF"
              onLoad={() => {
                setPdfLoading(false)
                setPdfError("")
              }}
              onError={() => {
                setPdfError("Failed to load PDF in iframe viewer")
              }}
            />
            <div className="mt-2 text-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setUseIframeViewer(false)
                  setPdfError("")
                }}
              >
                Switch to Advanced Viewer
              </Button>
            </div>
          </div>
        ) : (
          // React-PDF viewer
          <>
            {pdfLoading && (
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4" />
                  <p className="text-sm text-gray-600">Loading PDF document...</p>
                  <p className="text-xs text-gray-500 mt-1">Preparing pages for efficient viewing</p>
                </div>
              </div>
            )}
            
            {pdfError && (
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <div className="text-red-600 text-sm mb-4">{pdfError}</div>
                  <div className="space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        setPdfError("")
                        setPdfLoading(true)
                      }}
                    >
                      Retry
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        setUseIframeViewer(true)
                        setPdfError("")
                      }}
                    >
                      Use Simple Viewer
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleDownload}
                    >
                      Open in New Tab
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {!pdfLoading && !pdfError && (
              <>
                {console.log('Rendering Document component - pdfLoading:', pdfLoading, 'pdfError:', pdfError, 'numPages:', numPages)}
                <Document
                file={fileConfig}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                onLoadStart={onDocumentLoadStart}
                loading={<div className="flex items-center justify-center p-4">Loading PDF...</div>}
                error={<div className="flex items-center justify-center p-4 text-red-600">Error loading PDF</div>}
                options={pdfOptions}
              >
                {/* Render pages with progressive loading */}
                <div className="space-y-4 flex flex-col items-center">
                  {numPages && Array.from(new Array(numPages), (_, index) => {
                    const pageNum = index + 1

                    return (
                      <div
                        key={`page_${pageNum}`}
                        id={`pdf-page-${pageNum}`}
                        className="flex flex-col items-center"
                      >
                        {/* Page number indicator */}
                        <div className="mb-2 px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-full">
                          Page {pageNum}
                        </div>

                        <Page
                          pageNumber={pageNum}
                          renderTextLayer={false}
                          renderAnnotationLayer={false}
                          className="shadow-lg border border-gray-300"
                          scale={scale}
                          width={Math.min(800, window.innerWidth - 100)}
                          onLoadSuccess={() => {
                            console.log(`Page ${pageNum} loaded successfully`)
                          }}
                          onLoadError={(error) => {
                            console.error(`Page ${pageNum} load error:`, error)
                          }}
                        />
                      </div>
                    )
                  })}

                  {/* Load more trigger - positioned after last loaded page */}
                  {numPages && Math.max(...Array.from(loadedPages)) < numPages && (
                    <div
                      id="load-more-trigger"
                      className="h-1 w-full opacity-0"
                    />
                  )}

                  {/* Loading indicator for more pages */}
                  {isLoadingMore && (
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
                        <span className="text-sm text-gray-600">Loading more pages...</span>
                      </div>
                    </div>
                  )}
                </div>
              </Document>
              </>
            )}
          </>
        )}
      </div>
    </div>
  )
}
